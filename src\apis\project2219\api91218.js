/**
 * 接口名称：绑定会话
 * 接口路径：/ai/meeting/record/bind_conversation?id=:id
 * 文档地址：http://yapi.shinemo.com/project/2219/interface/api/91218
 **/

import service from '@/service'
export const api91218 = (data, config, id) =>
  service({
    url: `/aipaas-cgw/digital-ai-message/ai/meeting/record/bind_conversation?id=${id}`,
    method: 'post',
    data,
    config: { ...config, projectId: 2219, isRestful: false },
  })
