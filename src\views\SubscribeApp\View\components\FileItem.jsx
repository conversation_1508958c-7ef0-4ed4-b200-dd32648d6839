import React, { useEffect, useRef } from 'react'
import { Button, message } from 'antd'
import { useUpdateEffect } from 'ahooks'
import classnames from 'classnames'
import { getChatFileOptions } from 'constants/getChatFileOptions'
import XmiconReact from '@xm/icons-ai/dist/react'
import { FileList, useChatFile } from '@xm/ai.kit'

const FileItem = ({
  status,
  onChange,
  appInfo,
  updateAllToken = () => {},
  getStatus = () => {},
}) => {
  const preFileListRef = useRef([])
  const chatFileOptions = useChatFile({
    multiple: true,
    message,
    maxSize: 100 * 1024 * 1024,
    ...getChatFileOptions({
      maxToken: appInfo.models?.[0].contentMaxToken || 4 * 1024,
      appCode: appInfo.code,
      startToken: 0,
    }),
    // onChange: () => {
    //   const {fileList, statusInfo} = chatFileOptions
    //   const result = {
    //     maxToken: appInfo.models?.[0].inputMaxToken || 4096,
    //     appCode: appInfo.code,
    //     statusInfo,
    //     obsKeys: fileList.map((file) => {
    //       const objectKey = file.response?.obsKey
    //       return {
    //         obsKey: objectKey,
    //         rank: file.rank,
    //         fileInfo: JSON.stringify({
    //           fileName: file.name,
    //           fileSize: file.size,
    //           obsKey: objectKey,
    //           url: file.url,
    //         }),
    //       }
    //     }),
    //   }
    //   onChange(result)
    // }
  })
  const fileList = chatFileOptions.fileList || []

  useUpdateEffect(() => {
    // 阻止初始化的高亮
    if (
      preFileListRef.current.length === 0 &&
      chatFileOptions.fileList.length === 0
    ) {
      return
    }
    preFileListRef.current = [...chatFileOptions.fileList]
    const { fileList, statusInfo } = chatFileOptions
    const result = {
      maxToken: appInfo.models?.[0].contentMaxToken || 4 * 1024,
      appCode: appInfo.code,
      statusInfo,
      obsKeys: fileList.map((file) => {
        const objectKey = file.response?.obsKey
        return {
          obsKey: objectKey,
          rank: file.rank,
          fileInfo: JSON.stringify({
            fileName: file.name,
            fileSize: file.size,
            obsKey: objectKey,
            url: file.url,
          }),
        }
      }),
    }
    onChange(result)
  }, [chatFileOptions.statusInfo, appInfo, chatFileOptions.fileList])

  useEffect(() => {
    updateAllToken(chatFileOptions.statusInfo?.allToken || 0)
    return () => {
      updateAllToken(0)
    }
  }, [chatFileOptions.statusInfo])
  const { disabled = false, tips = '' } = getStatus() || {}

  return (
    <>
      <Button
        disabled={disabled}
        className={classnames(
          'relative [&:hover_.upload-btn-tips]:block w-1/3 flex justify-center items-center bg-#F7F8F9 focus:bg-#F7F8F9 hover:bg-#F7F8F9 rounded-lg text-#5C626B',
          '[&:active_i]:text-#7d9bff! [&:focus_i]:text-#7d9bff!',
          status === 'error' ? 'border-#ff997d! bg-#fff' : 'border-none!',
          {
            '[&:hover_i]:text-#7d9bff!': !disabled,
          },
        )}
        variant="filled"
        onClick={() => {
          // 打开文件选择框
          chatFileOptions.addFile()
        }}
      >
        <XmiconReact
          name="cloud_upload_line"
          size={16}
          color={disabled ? 'rgba(0, 0, 0, 0.25)' : '#00000099'}
        />
        {!!tips && (
          <div
            className={classnames(
              'upload-btn-tips absolute hidden bg-opacity-60! bg-#262A30 rounded-sm leading-[1.2] text-12 font-500 p-8 break-all white-space-nowrap text-#fff top--8 translate-y--100% left-50% translate-x--50%',
            )}
          >
            {tips}
          </div>
        )}
        <span className="ml-4">上传文件</span>
      </Button>
      {fileList?.length > 0 && (
        <FileList
          dataSource={fileList}
          onReParse={(file) => {
            chatFileOptions.parseFile(file)
          }}
          onRemove={(file, index) => {
            chatFileOptions.removeFile(index)
          }}
          direction="horizontal"
        />
      )}
    </>
  )
}

export default FileItem
