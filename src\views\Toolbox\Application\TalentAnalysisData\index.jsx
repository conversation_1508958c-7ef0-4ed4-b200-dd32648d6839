import ApplicationContainer from '../components/ApplicationContainer'
import Uploader from '../Contract/components/uploader'
import React, { useRef, useState } from 'react'
import { ProForm, ProTable } from '@ant-design/pro-components'
import {
  Badge,
  Button,
  Form,
  message,
  Modal,
  Space,
  Tooltip,
  Upload,
} from 'antd'
import { usePagination, useRequest } from 'ahooks'
import { api91245 } from 'apis/project2118/api91245'
import { api91246 } from 'apis/project2118/api91246'
import redmark from 'assets/img/redmark.png'
import { api91254 } from 'apis/project2118/api91254'
import { currentTableSource, getIconFromSuffix } from '@/util'
import { useNavigate } from 'react-router-dom'
import useBeforeUnload from 'hooks/useBeforeUnload'
import useConversationAppCode from '@/hooks/useConversationAppCode'

const TalentAnalysisData = () => {
  const formRef = useRef()
  const tableRef = useRef()
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const appCode = useConversationAppCode('TALENT_ANALYSIS_DATA')
  const [fileList, setFileList] = useState([])

  const handleDelete = (record) => {
    Modal.confirm({
      title: '确认删除吗?',
      content: '确认删除吗，删除后数据不可恢复',
      icon: (
        <img
          src={redmark}
          className="anticon anticon-exclamation-circle"
          style={{ width: '24px', height: '24px' }}
        />
      ),
      onOk: async () => {
        if (record.id) {
          await api91254({
            id: record.id,
          })
        }
        currentTableSource(tableRef, run, pagination)
        // run(pagination)
        message.success('删除成功')
      },
    })
  }

  const { run: handleUpload } = useRequest(
    async (params, uid) => {
      await api91245(params)
      setFileList((prev) => {
        return prev.map((item) => {
          if (item.uid === uid) {
            return {
              ...item,
              fileStatus: 'done',
            }
          }
          return item
        })
      })
      cancel()
      run({
        ...pagination,
        current: 1,
      })
    },
    {
      manual: true,
    },
  )

  const { data, run, loading, pagination, mutate, cancel } = usePagination(
    async ({ current, pageSize }) => {
      const res = await api91246({
        pageIndex: current,
        pageSize,
        bizCode: appCode,
      })

      setFileList((prevState) => {
        return prevState.filter((item) => {
          return item.fileStatus !== 'done'
        })
      })

      return {
        list: res?.list || [],
        total: res?.total,
      }
    },
    {
      // manual: true,
      showTotal: (t) => `共${t}条`,
      showSizeChanger: true,
      // showQuickJumper: true,
      defaultPageSize: 10,
      pollingInterval: 10000,
      debounceWait: 1000,
    },
  )
  const renderToolTip = (text) => {
    return text?.length > 20 ? (
      <Tooltip title={text}>{text.slice(0, 20)}...</Tooltip>
    ) : (
      text
    )
  }

  const columns = [
    {
      title: '文件',
      dataIndex: 'fileName',
      key: 'fileName',
      width: '300px',
      ellipsis: true,
      render: (_, record) => {
        return (
          <div className="flex items-center">
            <Badge
              className="flex items-center [&_.ant-badge-dot]:m-[4px_4px_0_0]"
              dot={!record?.isRead && record.status === 'COMPLETE'}
              color="#ff0000"
            >
              <img
                className="mr-4 w-24 h-24"
                src={getIconFromSuffix(record?.fileName)}
                alt=""
              />
            </Badge>
            {record?.status === 'COMPLETE' ? (
              <Button
                type="link"
                size="small"
                className="!inline-block text-primary cursor-pointer !px-0"
                onClick={() => {
                  navigate(`./detail/${record?.id}`)
                }}
              >
                {renderToolTip(record?.fileName)}
              </Button>
            ) : (
              renderToolTip(record?.fileName)
            )}
          </div>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '25%',
      valueType: 'select',
      valueEnum: {
        PROCESSING: {
          text: '上传处理中',
          color: '#FFC222',
        },
        FAILED: {
          text: '上传处理失败',
          color: '#FF5555',
        },
        COMPLETE: {
          text: '上传处理完成',
          color: '#09D999',
        },
      },
    },
    // 创建时间
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: '25%',
    },
    // 操作，支持查看和删除
    {
      title: '操作',
      key: '',
      dataIndex: '',
      width: '13%',
      render(_, record) {
        const { id, status } = record
        return (
          <Space>
            {status === 'COMPLETE' && (
              <Button
                className="!px-0"
                type="link"
                onClick={() => {
                  navigate(
                    `/toolbox/application/talent-analysis-data/detail/${id}`,
                  )
                }}
              >
                查看
              </Button>
            )}
            <Button
              onClick={() => {
                handleDelete(record)
              }}
              className="!px-0"
              type="link"
              danger
            >
              删除
            </Button>
          </Space>
        )
      },
    },
  ]

  const beforeunload = (e) => {
    const isUploading = fileList?.some(
      (item) => item.fileStatus === 'uploading',
    )
    if (isUploading) {
      e.preventDefault()
      e.returnValue = ''
      return true
    }
  }

  useBeforeUnload(beforeunload)

  return (
    <ApplicationContainer application="TalentAnalysisData">
      <ProForm formRef={formRef} form={form} submitter={false}>
        <ProForm.Item name="file" label="" noStyle>
          <Uploader
            maxCount={10}
            validFileTypeTxt="请上传xls/xlsx/csv格式文件"
            showItems={false}
            className="!mt-0 p-20 bg-white rounded-4 [&_.ant-upload]:h-240"
            handleMaxSize={(file) => {
              const isLtSize = file.size / 1024 / 1024 <= 5
              if (!isLtSize) {
                message.error('文件最大5M！')
                return Upload.LIST_IGNORE
              }
            }}
            onUpload={(file, _fileList, onChange) => {
              if (file.status === 'uploading' && file.percent === 0) {
                setFileList((prev) => [
                  {
                    fileName: file.name,
                    status: 'PROCESSING',
                    fileStatus: 'uploading',
                    uid: file?.originFileObj?.uid,
                  },
                  ...prev,
                ])
              } else if (file.status === 'done') {
                if (file.response?.code === 0) {
                  handleUpload(
                    {
                      fileName: file.response.data.objectMetadata?.filename,
                      fileType: file.response.data.objectMetadata?.filename
                        ?.split('.')
                        ?.pop(),
                      obsKey: file.response.data?.objectKey,
                      bizCode: appCode,
                    },
                    file?.originFileObj?.uid,
                  )
                } else {
                  message.error(file.response?.msg)
                }
              }
            }}
            accept=".xls,.xlsx,.csv"
            fileTypes={[
              'application/vnd.ms-excel',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'text/csv',
            ]}
            description="文档格式：支持xls/xlsx/csv，文件最大支持5M"
          />
        </ProForm.Item>
      </ProForm>
      <div className="text-16 text-#262A30 font-bold mt-28 mb-12">最近记录</div>
      <div className="bg-white p-20 rounded-4">
        <ProTable
          actionRef={tableRef}
          dataSource={[...fileList, ...(data?.list || [])]}
          loading={loading}
          expandable={false}
          pagination={pagination}
          columns={columns}
          rowKey="id"
          search={false}
          toolBarRender={false}
        />
      </div>
    </ApplicationContainer>
  )
}

export default TalentAnalysisData
