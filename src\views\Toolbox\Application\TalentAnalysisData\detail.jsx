import ApplicationHeader from 'views/Toolbox/Application/components/ApplicationHeader'
import { Tabs } from 'antd'
import { useParams } from 'react-router-dom'
import Chat from 'views/Toolbox/Application/components/Chat'
import { useRequest } from 'ahooks'
import { api91252 } from 'apis/project2118/api91252'
import PreviewMdContent from 'views/Toolbox/Application/components/PreviewMdContent'
import Loading from 'views/Toolbox/Application/components/Loading'
import FilePreview from 'views/Toolbox/Application/DocumentReader/components/FilePreview'
import { useEffect, useState } from 'react'
import useConversationAppCode from 'hooks/useConversationAppCode'
import { api91423 } from 'apis/project2118/api91423'

const TalentAnalysisDataDetail = () => {
  const params = useParams()
  const appCode = useConversationAppCode('TALENT_ANALYSIS_DATA')
  const [activeTab, setActiveTab] = useState('1')

  const {
    data: detail,
    run,
    loading,
    cancel,
  } = useRequest(
    async () => {
      if (!params?.id || activeTab !== '1') return detail
      return await api91252({
        id: params?.id,
      })
    },
    {
      refreshDeps: [params?.id],
      pollingInterval: 10000,
    },
  )

  console.log(loading, 'loadingloadingloading')

  useEffect(() => {
    if (detail && detail?.analysisStatus !== 'GENERATING') {
      cancel()
    }
  }, [detail?.analysisStatus])

  return (
    <ApplicationHeader back={true} title={detail?.fileName}>
      <div className="h-full flex gap-16 relative z-1 flex-1 min-h-0 px-16">
        <div className="flex-1 min-w-0 bg-#FBFCFE rounded-4 relative">
          {loading && !detail?.obsKey ? (
            <Loading className="w-full h-full" />
          ) : (
            <FilePreview
              url={`/aipaas-cgw/ai-tools/api/v1/oss-storage/object/download?obs_key=${detail?.obsKey}`}
              fileType={detail?.fileType}
            />
          )}
        </div>
        <div className="w-600 bg-white rounded-4 p-[0px_24px_16px_24px]">
          <Tabs
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key)
            }}
            className="h-full [&.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full [&_.ant-tabs-content]:h-full"
            items={[
              {
                key: '1',
                label: '分析报告',
                children: (
                  <PreviewMdContent
                    retry={async () => {
                      await api91423({
                        id: detail?.id,
                        bizCode: appCode,
                      })
                      run()
                    }}
                    reGenerate={async () => {
                      await api91423({
                        id: detail?.id,
                        bizCode: appCode,
                      })
                      run()
                    }}
                    label="分析报告"
                    loading={loading || detail?.analysisStatus === 'GENERATING'}
                    error={detail?.analysisStatus === 'FAILED'}
                    message={
                      detail?.analysisStatus === 'GENERATING'
                        ? ''
                        : detail?.analysisReport
                    }
                  />
                ),
              },
              {
                key: '2',
                label: '图表生成',
                children: (
                  <Chat
                    isEnableChat2DB={detail?.isEnableChat2DB}
                    type="chart"
                    appCode={`${detail?.graphPrefix}${appCode}`}
                    apiUrl="/aipaas-cgw/digital-ai-message/stream/ai/agent/data/analysis/graph"
                    conversationCodes={detail?.graphConversationIds}
                    // onSend={async (conversationId) => {
                    //   await api91352({
                    //     id: detail?.id,
                    //     conversationId,
                    //   })
                    // }}
                    getParams={({ conversationCode, content }) => {
                      return {
                        id: detail?.id,
                        bizCode: appCode,
                        conversationId: conversationCode,
                        content,
                      }
                    }}
                  />
                ),
              },
            ]}
          />
        </div>
      </div>
    </ApplicationHeader>
  )
}

export default TalentAnalysisDataDetail
