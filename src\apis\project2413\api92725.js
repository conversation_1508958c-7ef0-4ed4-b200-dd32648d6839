/**
 * 接口名称：查询租户知识库列表-文档阅读筛选数据使用
 * 接口路径：/knowledge/queryTenantKnowledge/filter
 * 文档地址：http://yapi.shinemo.com/project/2413/interface/api/92725
 **/

import service from '@/service'
export const api92725 = (data, config) =>
  service({
    url: '/aipaas-cgw/ai-paas/knowledge/queryTenantKnowledge/filter',
    method: 'post',
    data,
    config: { ...config, projectId: 2413, isRestful: false },
  })
