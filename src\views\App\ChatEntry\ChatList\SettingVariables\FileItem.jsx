import React, { useEffect, useRef } from 'react'
import XmiconReact from '@xm/icons-ai/dist/react'
import { Button, message, Tooltip } from 'antd'
import { getChatFileOptions } from 'constants/getChatFileOptions'
import { useChatContext } from '@/components/ChatHeadless/ChatContext'
import { FileList, useChatFile } from '@xm/ai.kit'
import { useUpdateEffect } from 'ahooks'
import classnames from 'classnames'

const FileItem = ({ onChange, updateAllToken, getStatus }) => {
  const initRef = useRef(null)
  const preFileListRef = useRef([])
  const { appInfo } = useChatContext()
  const chatFileOptions = useChatFile({
    multiple: true,
    message,
    maxSize: 100 * 1024 * 1024,
    ...getChatFileOptions({
      maxToken: appInfo.models?.[0].contentMaxToken || 4 * 1024,
      appCode: appInfo.code,
      startToken: 0,
    }),
    onChange: () => {
      const fileList = chatFileOptions.fileList
      const result = JSON.stringify({
        maxToken: appInfo.models?.[0].contentMaxToken || 4 * 1024,
        appCode: appInfo.code,
        obsKeys: fileList.map((file) => {
          const objectKey = file.response?.obsKey
          return {
            obsKey: objectKey,
            rank: file.rank,
            fileInfo: JSON.stringify({
              fileName: file.name,
              fileSize: file.size,
              obsKey: objectKey,
              url: file.url,
            }),
          }
        }),
      })
      onChange(result)
    },
  })
  useEffect(() => {
    updateAllToken(chatFileOptions.statusInfo?.allToken || 0)
    return () => {
      updateAllToken(0)
    }
  }, [chatFileOptions.statusInfo])
  const { disabled, tips } = getStatus()
  const fileList = chatFileOptions.fileList || []

  useUpdateEffect(() => {
    if (
      preFileListRef.current.length === 0 &&
      chatFileOptions.fileList.length === 0
    ) {
      return
    }
    preFileListRef.current = [...chatFileOptions.fileList]
    if (!initRef.current) {
      initRef.current = true
    } else {
      const { fileList, statusInfo } = chatFileOptions
      const result = JSON.stringify({
        maxToken: appInfo.models?.[0].contentMaxToken || 4 * 1024,
        appCode: appInfo.code,
        statusInfo,
        obsKeys: fileList.map((file) => {
          const objectKey = file.response?.obsKey
          return {
            obsKey: objectKey,
            rank: file.rank,
            fileInfo: JSON.stringify({
              fileName: file.name,
              fileSize: file.size,
              obsKey: objectKey,
              url: file.url,
            }),
          }
        }),
      })
      onChange(result)
    }
  }, [chatFileOptions.statusInfo, appInfo, chatFileOptions.fileList])
  const uploadBtn = (
    <Button
      disabled={disabled}
      className={classnames(
        'w-full flex justify-center items-center text-#5C626B border-none [&_button]:w-full',
        {
          ' [&:hover_i]:text-#7d9bff! [&:active_i]:text-#7d9bff! [&:focus_i]:text-#7d9bff! ':
            !disabled,
        },
      )}
      onClick={() => {
        // 打开文件选择框
        chatFileOptions.addFile()
      }}
    >
      <XmiconReact
        name="cloud_upload_line"
        size={16}
        color={disabled ? 'rgba(0, 0, 0, 0.25)' : '#00000099'}
      />
      <span className="ml-4">上传文件</span>
    </Button>
  )
  return (
    <div className="[&_.ai-file-item]:bg-#fff">
      {tips ? <Tooltip title={tips}>{uploadBtn}</Tooltip> : uploadBtn}
      {fileList?.length > 0 && (
        <FileList
          dataSource={fileList}
          onReParse={(file) => {
            chatFileOptions.parseFile(file)
          }}
          onRemove={(file, index) => {
            chatFileOptions.removeFile(index)
          }}
          direction="horizontal"
        />
      )}
    </div>
  )
}

export default FileItem
